from sqlalchemy import Column, <PERSON>, Integer, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from typing import List
from pydantic import BaseModel, Field, ConfigDict

Base = declarative_base()

class Document(Base):
    __tablename__ = "documents"
    
    id = Column(String, primary_key=True)
    tenant_id = Column(String, nullable=False)
    document_name = Column(String, nullable=False)
    document_type = Column(String, nullable=False)
    es_index = Column(String)
    es_document_id = Column(String)
    s3_key = Column(String)  # Add S3 key field
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String)

class Chatbot(Base):
    __tablename__ = "chatbots"

    id = Column(String, primary_key=True, index=True)
    tenant_id = Column(String, index=True)
    name = Column(String)
    type = Column(String, default="AI")  # 'AI' or 'RULE'
    description = Column(String, nullable=True)
    status = Column(String, default="DRAFT")  # 'DRAFT', 'ACTIVE', 'INACTIVE'
    welcome_message = Column(String, nullable=True)
    thank_you_message = Column(String, nullable=True)
    # Connected account fields
    connected_account_display_name = Column(String, nullable=True)
    entity_type = Column(String, nullable=True)
    connected_account_id = Column(Integer, nullable=True)
    # Trigger field for entity creation flow
    trigger = Column(String, nullable=True)  # 'NEW_ENTITY' or 'EXISTING_ENTITY'
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ChatbotQuestion(Base):
    __tablename__ = "chatbot_questions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, nullable=False, index=True)
    tenant_id = Column(String, nullable=False, index=True)
    question = Column(String(100), nullable=False)
    field_id = Column(Integer, nullable=False)
    display_name = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class ChatbotKnowledgebase(Base):
    __tablename__ = "chatbot_knowledgebases"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, nullable=False, index=True)
    document_id = Column(String, nullable=False, index=True)
    tenant_id = Column(String, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class ChatbotConversation(Base):
    __tablename__ = "chatbot_conversations"
    
    id = Column(String, primary_key=True, index=True)
    chatbot_id = Column(String, ForeignKey("chatbots.id"))
    tenant_id = Column(String, index=True)
    user_id = Column(String, index=True)  # Add user_id field
    conversation_data = Column(Text)
    completed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with token usage
    token_usage = relationship(
        "ConversationTokenUsage", 
        back_populates="conversation",
        cascade="all, delete-orphan"
    )

class ConversationTokenUsage(Base):
    __tablename__ = "conversation_token_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("chatbot_conversations.id"), index=True)
    tenant_id = Column(String, index=True)
    input = Column(JSON)  # JSONB column containing user message and system prompt
    output = Column(JSON)  # JSONB column containing LLM-generated response
    input_tokens = Column(Integer, default=0)  # Tokens in input prompt sent to LLM
    output_tokens = Column(Integer, default=0)  # Tokens in response generated by LLM
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationship with conversation
    conversation = relationship("ChatbotConversation", back_populates="token_usage")

class ChatbotCreditUsage(Base):
    __tablename__ = "chatbot_credit_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), index=True)
    conversation_id = Column(String, ForeignKey("chatbot_conversations.id"), index=True)
    tenant_id = Column(String, index=True)
    question = Column(String, nullable=False)  # The question that was asked
    answer = Column(String, nullable=False)    # The answer that was provided
    credits_used = Column(Integer, default=0)  # Credits consumed (1 or 2)
    has_knowledgebase = Column(Boolean, default=False)  # Whether knowledgebase was available
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    chatbot = relationship("Chatbot")
    conversation = relationship("ChatbotConversation")

# JWT Token models
class TokenAction(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    read: bool = False
    write: bool = False
    update: bool = False
    delete: bool = False
    email: bool = False
    call: bool = False
    sms: bool = False
    task: bool = False
    note: bool = False
    meeting: bool = False
    document: bool = False
    readAll: bool = False
    updateAll: bool = False
    deleteAll: bool = False
    quotation: bool = False
    reshare: bool = False
    reassign: bool = False

class TokenPermission(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    id: int
    name: str
    description: str
    limits: int
    units: str
    action: TokenAction

class TokenSource(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    type: str
    id: str
    name: str

class TokenMeta(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    rate_limit: int = Field(alias="rate-limit")
    pid: int

class TokenData(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    accessToken: str
    expiresIn: int
    expiry: int
    tokenType: str
    refreshToken: str
    permissions: List[TokenPermission]
    userId: str
    username: str
    tenantId: str
    source: TokenSource
    meta: TokenMeta

class JWTPayload(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    iss: str
    data: TokenData

# Pydantic models for API requests/responses
class QuestionCreate(BaseModel):
    question: str
    fieldId: int
    displayName: str

class QuestionUpdate(BaseModel):
    question: str = None
    fieldId: int = None
    displayName: str = None

class ConnectedAccount(BaseModel):
    displayName: str
    entityType: str  # e.g., 'LEAD'
    accountId: int

class ChatbotCreate(BaseModel):
    name: str
    type: str  # 'AI' or 'RULE'
    description: str = None
    welcomeMessage: str = None
    thankYouMessage: str = None
    connectedAccount: ConnectedAccount = None
    trigger: str = None  # 'NEW_ENTITY' or 'EXISTING_ENTITY'

class ChatbotUpdate(BaseModel):
    name: str = None
    description: str = None
    welcomeMessage: str = None
    thankYouMessage: str = None
    connectedAccount: ConnectedAccount = None
    trigger: str = None  # 'NEW_ENTITY' or 'EXISTING_ENTITY'
    questions: List[QuestionCreate] = None
    knowledgebase_ids: List[str] = None

class ConversationMessage(BaseModel):
    message: str

class ConversationRequest(BaseModel):
    message: str
    entityType: str  # e.g., 'LEAD'
    connectedAccountId: int
    trigger: str  # 'NEW_ENTITY' or 'EXISTING_ENTITY'

class ConversationResponse(BaseModel):
    conversation_id: str
    answer: str = None
    nextQuestion: str = None

class CreditUsageResponse(BaseModel):
    id: str
    chatbot_id: str
    conversation_id: str
    question: str
    answer: str
    credits_used: int
    has_knowledgebase: bool
    timestamp: datetime
    message: str = None
    completed: bool = False
    verification_pending: bool = False
    answers: List[dict] = None
    is_knowledge_response: bool = False
    is_off_topic: bool = False
    ended: bool = False
