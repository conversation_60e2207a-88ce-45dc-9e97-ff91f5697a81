#!/usr/bin/env python3
"""
Test script to verify the new chatbot request structure works correctly.
This script tests the new camelCase field names and nested connectedAccount structure.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
BASE_URL = "http://localhost:8000"  # Adjust if your API runs on a different port
API_BASE = f"{BASE_URL}/api/v1/chatbots"

# Test data with new structure
test_chatbot_data = {
    "name": "Test Chatbot with Connected Account",
    "type": "AI",
    "description": "A test chatbot to verify the new request structure",
    "welcomeMessage": "Hello! Welcome to our test chatbot.",
    "thankYouMessage": "Thank you for using our test chatbot. Goodbye!",
    "connectedAccount": {
        "displayName": "My Test Account",
        "entityType": "LEAD",
        "accountId": 12345
    }
}

# Test data without connected account
test_chatbot_data_minimal = {
    "name": "Minimal Test Chatbot",
    "type": "AI",
    "description": "A minimal test chatbot without connected account"
}

def get_auth_headers():
    """
    Get authentication headers. 
    You may need to modify this based on your authentication system.
    """
    # For testing purposes, you might need to add proper authentication
    # This is a placeholder - adjust according to your auth system
    return {
        "Content-Type": "application/json",
        # Add your authentication headers here
        # "Authorization": "Bearer your-token-here"
    }

def test_create_chatbot_with_connected_account():
    """Test creating a chatbot with the new connected account structure"""
    print("Testing chatbot creation with connected account...")
    
    headers = get_auth_headers()
    
    try:
        response = requests.post(API_BASE, json=test_chatbot_data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("✓ Chatbot created successfully with connected account!")
            return response.json()
        else:
            print(f"✗ Failed to create chatbot: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"✗ Failed to parse response JSON: {e}")
        print(f"Raw response: {response.text}")
        return None

def test_create_chatbot_minimal():
    """Test creating a chatbot without connected account"""
    print("\nTesting chatbot creation without connected account...")
    
    headers = get_auth_headers()
    
    try:
        response = requests.post(API_BASE, json=test_chatbot_data_minimal, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("✓ Minimal chatbot created successfully!")
            return response.json()
        else:
            print(f"✗ Failed to create minimal chatbot: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"✗ Failed to parse response JSON: {e}")
        print(f"Raw response: {response.text}")
        return None

def test_get_chatbot(chatbot_id):
    """Test retrieving a chatbot to verify the response structure"""
    print(f"\nTesting chatbot retrieval for ID: {chatbot_id}...")
    
    headers = get_auth_headers()
    
    try:
        response = requests.get(f"{API_BASE}/{chatbot_id}", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✓ Chatbot retrieved successfully!")
            return response.json()
        else:
            print(f"✗ Failed to retrieve chatbot: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"✗ Failed to parse response JSON: {e}")
        print(f"Raw response: {response.text}")
        return None

def test_update_chatbot(chatbot_id):
    """Test updating a chatbot with new structure"""
    print(f"\nTesting chatbot update for ID: {chatbot_id}...")
    
    update_data = {
        "name": "Updated Test Chatbot",
        "welcomeMessage": "Hello! This is an updated welcome message.",
        "connectedAccount": {
            "displayName": "Updated Account Name",
            "entityType": "CUSTOMER",
            "accountId": 67890
        }
    }
    
    headers = get_auth_headers()
    
    try:
        response = requests.put(f"{API_BASE}/{chatbot_id}", json=update_data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✓ Chatbot updated successfully!")
            return response.json()
        else:
            print(f"✗ Failed to update chatbot: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"✗ Failed to parse response JSON: {e}")
        print(f"Raw response: {response.text}")
        return None

def main():
    """Run all tests"""
    print("Starting chatbot API tests with new structure...\n")
    
    # Test 1: Create chatbot with connected account
    chatbot_with_account = test_create_chatbot_with_connected_account()
    
    # Test 2: Create minimal chatbot
    minimal_chatbot = test_create_chatbot_minimal()
    
    # Test 3: Retrieve chatbot (if creation was successful)
    if chatbot_with_account and 'id' in chatbot_with_account:
        retrieved_chatbot = test_get_chatbot(chatbot_with_account['id'])
        
        # Test 4: Update chatbot
        updated_chatbot = test_update_chatbot(chatbot_with_account['id'])
    
    print("\n" + "="*50)
    print("Test Summary:")
    print("- Create with connected account:", "✓" if chatbot_with_account else "✗")
    print("- Create minimal:", "✓" if minimal_chatbot else "✗")
    if chatbot_with_account:
        print("- Retrieve chatbot:", "✓" if 'retrieved_chatbot' in locals() and retrieved_chatbot else "✗")
        print("- Update chatbot:", "✓" if 'updated_chatbot' in locals() and updated_chatbot else "✗")
    print("="*50)

if __name__ == "__main__":
    main()
