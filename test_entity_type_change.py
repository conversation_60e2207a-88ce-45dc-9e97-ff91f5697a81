#!/usr/bin/env python3
"""
Simple test to verify the entity_type column change is working correctly.
This test focuses on database operations without requiring all application dependencies.
"""

import os
import sys

def test_database_schema():
    """Test that the database schema has been updated correctly"""
    print("Testing database schema changes...")
    
    # Test using Docker to avoid dependency issues
    import subprocess
    
    try:
        # Test 1: Verify entity_type column exists
        result = subprocess.run([
            'docker', 'exec', 'sd-whatsapp-postgres', 'psql', 
            '-U', 'sdwhatsapp', '-d', 'sdwhatsapp', '-t', '-c',
            "SELECT column_name FROM information_schema.columns WHERE table_name = 'chatbots' AND column_name = 'entity_type';"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and 'entity_type' in result.stdout:
            print("  ✓ entity_type column exists")
        else:
            print("  ✗ entity_type column not found")
            return False
        
        # Test 2: Verify old column name doesn't exist
        result = subprocess.run([
            'docker', 'exec', 'sd-whatsapp-postgres', 'psql', 
            '-U', 'sdwhatsapp', '-d', 'sdwhatsapp', '-t', '-c',
            "SELECT column_name FROM information_schema.columns WHERE table_name = 'chatbots' AND column_name = 'connected_account_entity_type';"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and 'connected_account_entity_type' not in result.stdout.strip():
            print("  ✓ old connected_account_entity_type column removed")
        else:
            print("  ✗ old connected_account_entity_type column still exists")
            return False
        
        # Test 3: Test data insertion and retrieval
        test_sql = """
        BEGIN;
        INSERT INTO chatbots (id, tenant_id, name, type, entity_type, connected_account_display_name, connected_account_id) 
        VALUES ('test-entity-type-123', 'test-tenant', 'Test Chatbot', 'AI', 'CUSTOMER', 'Test Account', 54321);
        
        SELECT entity_type FROM chatbots WHERE id = 'test-entity-type-123';
        
        ROLLBACK;
        """
        
        result = subprocess.run([
            'docker', 'exec', 'sd-whatsapp-postgres', 'psql', 
            '-U', 'sdwhatsapp', '-d', 'sdwhatsapp', '-t', '-c', test_sql
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and 'CUSTOMER' in result.stdout:
            print("  ✓ entity_type column accepts and returns data correctly")
        else:
            print("  ✗ entity_type column data operations failed")
            print(f"    Output: {result.stdout}")
            print(f"    Error: {result.stderr}")
            return False
        
        print("✓ Database schema test passed")
        return True
        
    except subprocess.TimeoutExpired:
        print("  ✗ Database test timed out")
        return False
    except Exception as e:
        print(f"  ✗ Database test failed: {str(e)}")
        return False

def test_model_field_names():
    """Test that the model field names have been updated in the code"""
    print("\nTesting model field name changes...")
    
    try:
        # Test 1: Check app/models.py has the new field name
        with open('app/models.py', 'r') as f:
            models_content = f.read()
        
        if 'entity_type = Column(String, nullable=True)' in models_content:
            print("  ✓ Chatbot model has entity_type field")
        else:
            print("  ✗ Chatbot model missing entity_type field")
            return False
        
        if 'connected_account_entity_type' not in models_content:
            print("  ✓ Old connected_account_entity_type field removed from model")
        else:
            print("  ✗ Old connected_account_entity_type field still in model")
            return False
        
        # Test 2: Check service file has been updated
        with open('app/services/chatbot_service.py', 'r') as f:
            service_content = f.read()
        
        if 'chatbot.entity_type' in service_content:
            print("  ✓ Service layer uses entity_type field")
        else:
            print("  ✗ Service layer missing entity_type field usage")
            return False
        
        if 'connected_account_entity_type' not in service_content:
            print("  ✓ Old connected_account_entity_type field removed from service")
        else:
            print("  ✗ Old connected_account_entity_type field still in service")
            return False
        
        print("✓ Model field name test passed")
        return True
        
    except Exception as e:
        print(f"  ✗ Model field name test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("Starting entity_type column change verification tests...\n")
    
    # Change to the correct directory
    os.chdir('/home/<USER>/repo/sd-whatsapp-chatbot')
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Database schema
    if test_database_schema():
        tests_passed += 1
    
    # Test 2: Model field names
    if test_model_field_names():
        tests_passed += 1
    
    print(f"\n============================================================")
    print(f"TEST SUMMARY")
    print(f"============================================================")
    print(f"Database Schema      {'✓ PASSED' if tests_passed >= 1 else '✗ FAILED'}")
    print(f"Model Field Names    {'✓ PASSED' if tests_passed >= 2 else '✗ FAILED'}")
    print(f"------------------------------------------------------------")
    print(f"Total: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The entity_type column change is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
