#!/usr/bin/env python3
"""
Migration script to add new fields to chatbot_questions table:
- field_id (INTEGER, NOT NULL)
- display_name (VARCHAR, NOT NULL)

Run this script to add the new columns to the existing chatbot_questions table.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_database_url():
    """Get database URL from environment variables"""
    DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
    DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
    DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
    DB_PORT = "5432"
    DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")
    return f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_migration():
    """
    Add new fields to the chatbot_questions table
    """
    try:
        # Get database URL
        database_url = get_database_url()
        logger.info(f"Connecting to database...")
        
        # Create engine
        engine = create_engine(database_url)
        
        # SQL statements to add new columns
        migration_sql = [
            """
            ALTER TABLE chatbot_questions 
            ADD COLUMN IF NOT EXISTS field_id INTEGER;
            """,
            """
            ALTER TABLE chatbot_questions 
            ADD COLUMN IF NOT EXISTS display_name VARCHAR;
            """,
            # Set default values for existing records (if any)
            """
            UPDATE chatbot_questions 
            SET field_id = 0, display_name = 'Default Field'
            WHERE field_id IS NULL OR display_name IS NULL;
            """,
            # Make the columns NOT NULL after setting default values
            """
            ALTER TABLE chatbot_questions 
            ALTER COLUMN field_id SET NOT NULL;
            """,
            """
            ALTER TABLE chatbot_questions 
            ALTER COLUMN display_name SET NOT NULL;
            """
        ]
        
        # Execute migration
        with engine.connect() as connection:
            # Start transaction
            trans = connection.begin()
            
            try:
                logger.info("Starting migration: Adding question fields...")
                
                for i, sql in enumerate(migration_sql, 1):
                    logger.info(f"Executing migration step {i}/{len(migration_sql)}...")
                    connection.execute(text(sql))
                    logger.info(f"✓ Migration step {i} completed")
                
                # Commit transaction
                trans.commit()
                logger.info("✓ Migration completed successfully!")
                
                # Verify the columns were added
                logger.info("Verifying new columns...")
                result = connection.execute(text("""
                    SELECT column_name, data_type, is_nullable 
                    FROM information_schema.columns 
                    WHERE table_name = 'chatbot_questions' 
                    AND column_name IN ('field_id', 'display_name')
                    ORDER BY column_name;
                """))
                
                columns = result.fetchall()
                logger.info("Current question columns:")
                for column in columns:
                    logger.info(f"  - {column[0]}: {column[1]} (nullable: {column[2]})")
                
                if len(columns) == 2:
                    logger.info("✓ All question fields are present")
                else:
                    logger.warning(f"⚠ Expected 2 columns, found {len(columns)}")
                
            except Exception as e:
                # Rollback transaction on error
                trans.rollback()
                logger.error(f"Migration failed: {str(e)}")
                raise
                
    except SQLAlchemyError as e:
        logger.error(f"Database error during migration: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during migration: {str(e)}")
        raise


def check_migration_needed():
    """
    Check if the migration is needed by verifying current table structure
    """
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        with engine.connect() as connection:
            # Check if new columns exist
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chatbot_questions' 
                AND column_name IN ('field_id', 'display_name')
            """))
            
            existing_columns = [row[0] for row in result.fetchall()]
            
            if len(existing_columns) < 2:
                logger.info("Migration is needed")
                return True
            else:
                logger.info("Migration not needed - columns already exist")
                return False
                
    except Exception as e:
        logger.error(f"Error checking migration status: {str(e)}")
        return True  # Assume migration is needed if we can't check


def rollback_migration():
    """
    Rollback the migration by dropping the added columns
    """
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        rollback_sql = [
            "ALTER TABLE chatbot_questions DROP COLUMN IF EXISTS field_id;",
            "ALTER TABLE chatbot_questions DROP COLUMN IF EXISTS display_name;"
        ]
        
        with engine.connect() as connection:
            trans = connection.begin()
            try:
                logger.info("Starting rollback...")
                for i, sql in enumerate(rollback_sql, 1):
                    logger.info(f"Executing rollback step {i}/{len(rollback_sql)}...")
                    connection.execute(text(sql))
                
                trans.commit()
                logger.info("✓ Rollback completed successfully!")
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Rollback failed: {str(e)}")
                raise
                
    except Exception as e:
        logger.error(f"Error during rollback: {str(e)}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate question fields in chatbot_questions table")
    parser.add_argument("--check", action="store_true", help="Check if migration is needed")
    parser.add_argument("--rollback", action="store_true", help="Rollback the migration")
    
    args = parser.parse_args()
    
    if args.check:
        needed = check_migration_needed()
        sys.exit(0 if not needed else 1)
    elif args.rollback:
        rollback_migration()
    else:
        if check_migration_needed():
            run_migration()
        else:
            logger.info("Migration not needed")
