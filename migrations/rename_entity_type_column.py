#!/usr/bin/env python3
"""
Migration script to rename connected_account_entity_type to entity_type in chatbots table.

This migration:
- Renames connected_account_entity_type column to entity_type
- Preserves all existing data
- Includes rollback functionality

Run this script to update the column name in the chatbots table.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = "5432"
DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def run_migration():
    """Rename connected_account_entity_type to entity_type in chatbots table"""
    
    try:
        engine = create_engine(DATABASE_URL)
        logger.info(f"Connecting to database: {DB_HOST}:{DB_PORT}/{DB_NAME}")
        
        with engine.begin() as connection:
            
            # Check if the old column exists
            logger.info("Checking if connected_account_entity_type column exists...")
            check_old_column = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name = 'connected_account_entity_type'
            """))
            
            old_column_exists = check_old_column.fetchone() is not None
            
            # Check if the new column already exists
            logger.info("Checking if entity_type column already exists...")
            check_new_column = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name = 'entity_type'
            """))
            
            new_column_exists = check_new_column.fetchone() is not None
            
            if not old_column_exists and new_column_exists:
                logger.info("✓ Migration already completed - entity_type column exists and connected_account_entity_type does not")
                return True
            
            if not old_column_exists:
                logger.warning("⚠ connected_account_entity_type column does not exist - nothing to migrate")
                return True
            
            if new_column_exists:
                logger.error("✗ Both columns exist - manual intervention required")
                return False
            
            # Perform the migration
            logger.info("Starting migration: Renaming connected_account_entity_type to entity_type...")
            
            # Rename the column
            rename_sql = """
            ALTER TABLE chatbots 
            RENAME COLUMN connected_account_entity_type TO entity_type;
            """
            
            connection.execute(text(rename_sql))
            logger.info("✓ Column renamed successfully")
            
            # Verify the migration
            logger.info("Verifying migration...")
            verify_result = connection.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name = 'entity_type'
            """))
            
            verification = verify_result.fetchone()
            if verification:
                logger.info(f"✓ Migration verified - entity_type column exists: {verification[1]}, nullable: {verification[2]}")
            else:
                logger.error("✗ Migration verification failed - entity_type column not found")
                return False
            
            logger.info("✓ Migration completed successfully!")
            return True
            
    except SQLAlchemyError as e:
        logger.error(f"Database error during migration: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during migration: {str(e)}")
        return False

def rollback_migration():
    """Rollback the migration by renaming entity_type back to connected_account_entity_type"""
    
    try:
        engine = create_engine(DATABASE_URL)
        logger.info(f"Connecting to database for rollback: {DB_HOST}:{DB_PORT}/{DB_NAME}")
        
        with engine.begin() as connection:
            
            # Check if entity_type column exists
            check_new_column = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name = 'entity_type'
            """))
            
            new_column_exists = check_new_column.fetchone() is not None
            
            if not new_column_exists:
                logger.warning("⚠ entity_type column does not exist - nothing to rollback")
                return True
            
            # Perform the rollback
            logger.info("Starting rollback: Renaming entity_type back to connected_account_entity_type...")
            
            rollback_sql = """
            ALTER TABLE chatbots 
            RENAME COLUMN entity_type TO connected_account_entity_type;
            """
            
            connection.execute(text(rollback_sql))
            logger.info("✓ Rollback completed successfully!")
            return True
            
    except SQLAlchemyError as e:
        logger.error(f"Database error during rollback: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during rollback: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        print("Running rollback migration...")
        success = rollback_migration()
    else:
        print("Running forward migration...")
        success = run_migration()
    
    if success:
        print("Migration completed successfully!")
        sys.exit(0)
    else:
        print("Migration failed!")
        sys.exit(1)
