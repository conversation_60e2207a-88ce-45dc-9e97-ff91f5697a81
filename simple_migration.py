#!/usr/bin/env python3
"""
Simple database migration script that doesn't depend on app imports
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_url():
    """Get database URL from environment variables"""
    # Try different environment variable names
    database_url = (
        os.getenv("DATABASE_URL") or
        os.getenv("DB_URL") or
        os.getenv("POSTGRES_URL") or
        os.getenv("POSTGRESQL_URL")
    )
    
    if not database_url:
        # Try to construct from individual components
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "5432")
        user = os.getenv("DB_USER", "postgres")
        password = os.getenv("DB_PASSWORD", "")
        database = os.getenv("DB_NAME", "chatbot_db")
        
        if password:
            database_url = f"postgresql://{user}:{password}@{host}:{port}/{database}"
        else:
            database_url = f"postgresql://{user}@{host}:{port}/{database}"
    
    return database_url

def run_migration():
    """Run the migration to add trigger column and credit usage table"""
    
    try:
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
    except ImportError:
        print("❌ SQLAlchemy not installed. Please run:")
        print("pip install sqlalchemy psycopg2-binary")
        return False
    
    # Get database URL
    database_url = get_database_url()
    
    if not database_url:
        print("❌ Database URL not found. Please set one of:")
        print("  - DATABASE_URL")
        print("  - DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME")
        return False
    
    print(f"🔗 Connecting to database...")
    
    try:
        # Create engine
        engine = create_engine(database_url)
        
        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()
        
        logger.info("Starting migration: add_trigger_and_credit_usage")
        
        # 1. Add trigger column to chatbots table
        logger.info("Adding trigger column to chatbots table...")
        session.execute(text("""
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS trigger VARCHAR;
        """))
        
        # 2. Create chatbot_credit_usage table
        logger.info("Creating chatbot_credit_usage table...")
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS chatbot_credit_usage (
                id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
                chatbot_id VARCHAR NOT NULL,
                conversation_id VARCHAR NOT NULL,
                tenant_id VARCHAR NOT NULL,
                question VARCHAR NOT NULL,
                answer VARCHAR NOT NULL,
                credits_used INTEGER DEFAULT 0,
                has_knowledgebase BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """))
        
        # 3. Create indexes for better performance
        logger.info("Creating indexes for chatbot_credit_usage table...")
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_chatbot_id 
            ON chatbot_credit_usage(chatbot_id);
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_conversation_id 
            ON chatbot_credit_usage(conversation_id);
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_tenant_id 
            ON chatbot_credit_usage(tenant_id);
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_timestamp 
            ON chatbot_credit_usage(timestamp);
        """))
        
        # Commit the changes
        session.commit()
        logger.info("✅ Migration completed successfully!")
        
        # Verify migration
        verify_migration(session)
        
        session.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
        return False

def verify_migration(session):
    """Verify that the migration was applied correctly"""
    
    try:
        logger.info("Verifying migration...")
        
        # Check if trigger column exists in chatbots table
        result = session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chatbots' AND column_name = 'trigger';
        """))
        
        if result.fetchone():
            logger.info("✅ trigger column exists in chatbots table")
        else:
            logger.error("❌ trigger column missing in chatbots table")
            return False
        
        # Check if chatbot_credit_usage table exists
        result = session.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'chatbot_credit_usage';
        """))
        
        if result.fetchone():
            logger.info("✅ chatbot_credit_usage table exists")
        else:
            logger.error("❌ chatbot_credit_usage table missing")
            return False
        
        logger.info("✅ Migration verification completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration verification failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("🗄️  Database Migration: Add Trigger and Credit Usage")
    print("=" * 60)
    
    if "--help" in sys.argv or "-h" in sys.argv:
        print("Usage: python3 simple_migration.py")
        print("\nEnvironment variables:")
        print("  DATABASE_URL - Full database URL")
        print("  OR individual components:")
        print("  DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME")
        return
    
    success = run_migration()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("\nNext steps:")
        print("1. Test the application")
        print("2. Run comprehensive tests")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
