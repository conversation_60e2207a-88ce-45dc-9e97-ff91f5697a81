#!/usr/bin/env python3
"""
Test script to verify the updated questions API with fieldId and displayName:
- Test the new QuestionCreate model structure
- Test the database model changes
- Test the service layer methods
- Test JSON serialization/deserialization

This script tests the models and service layer directly.
"""

import json
import sys
import os
from typing import Dict, Any, List

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_question_database_model():
    """Test that the ChatbotQuestion database model includes the new fields"""
    print("Testing ChatbotQuestion database model...")
    
    try:
        from app.models import ChatbotQuestion
        
        # Check if the new fields exist in the model
        model_fields = [attr for attr in dir(ChatbotQuestion) if not attr.startswith('_')]
        
        required_fields = ['field_id', 'display_name']
        found_fields = []
        
        for field in required_fields:
            if field in model_fields:
                found_fields.append(field)
                print(f"  ✓ {field} field found in ChatbotQuestion model")
            else:
                print(f"  ✗ {field} field not found in ChatbotQuestion model")
        
        if len(found_fields) == len(required_fields):
            print("✓ ChatbotQuestion database model test passed")
            return True
        else:
            print(f"✗ ChatbotQuestion database model test failed - {len(required_fields) - len(found_fields)} fields missing")
            return False
            
    except Exception as e:
        print(f"✗ ChatbotQuestion database model test failed: {str(e)}")
        return False


def test_question_create_model():
    """Test that the QuestionCreate Pydantic model supports the new structure"""
    print("\nTesting QuestionCreate Pydantic model...")
    
    try:
        from app.models import QuestionCreate
        
        # Test QuestionCreate with new structure
        print("  Testing QuestionCreate model...")
        question_data = {
            "question": "What is your state?",
            "fieldId": 129281,
            "displayName": "State"
        }
        
        question_create = QuestionCreate(**question_data)
        print(f"    ✓ QuestionCreate created: {question_create.question}")
        print(f"    ✓ fieldId: {question_create.fieldId}")
        print(f"    ✓ displayName: {question_create.displayName}")
        
        # Test with different data
        question_data2 = {
            "question": "What is your name?",
            "fieldId": 129282,
            "displayName": "Full Name"
        }
        
        question_create2 = QuestionCreate(**question_data2)
        print(f"    ✓ Second QuestionCreate created: {question_create2.question}")
        
        print("✓ QuestionCreate Pydantic model test passed")
        return True
        
    except Exception as e:
        print(f"✗ QuestionCreate Pydantic model test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_service_layer_questions():
    """Test that the service layer handles the new question structure correctly"""
    print("\nTesting service layer for questions...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        from app.models import QuestionCreate
        
        # Create test questions data
        questions_data = [
            QuestionCreate(
                question="What is your state?",
                fieldId=129281,
                displayName="State"
            ),
            QuestionCreate(
                question="What is your name?",
                fieldId=129282,
                displayName="Full Name"
            )
        ]
        
        # Test service instantiation
        service = ChatbotService()
        print("  ✓ ChatbotService instantiated")
        
        # Check if configure_chatbot_questions method exists and has correct signature
        import inspect
        configure_method = getattr(service, 'configure_chatbot_questions', None)
        if configure_method:
            sig = inspect.signature(configure_method)
            params = list(sig.parameters.keys())
            if 'chatbot_id' in params and 'questions' in params and 'tenant_id' in params:
                print("  ✓ configure_chatbot_questions method has correct signature")
            else:
                print(f"  ✗ configure_chatbot_questions method signature incorrect: {params}")
                return False
        else:
            print("  ✗ configure_chatbot_questions method not found")
            return False
        
        print("✓ Service layer questions test passed")
        return True
        
    except Exception as e:
        print(f"✗ Service layer questions test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_questions_json_serialization():
    """Test that the new question structure can be properly serialized/deserialized"""
    print("\nTesting questions JSON serialization...")
    
    try:
        from app.models import QuestionCreate
        
        # Create test data matching the required format
        test_questions_json = [
            {
                "question": "What is your state?",
                "fieldId": 129281,
                "displayName": "State"
            },
            {
                "question": "What is your name?",
                "fieldId": 129282,
                "displayName": "Full Name"
            },
            {
                "question": "What is your email?",
                "fieldId": 129283,
                "displayName": "Email Address"
            }
        ]
        
        # Test deserialization from JSON
        questions = [QuestionCreate(**q_data) for q_data in test_questions_json]
        print(f"  ✓ Deserialization from JSON successful - {len(questions)} questions created")
        
        # Test serialization to dict
        questions_dict = [q.model_dump() for q in questions]
        print("  ✓ Serialization to dict successful")
        
        # Verify structure
        for i, q_dict in enumerate(questions_dict):
            assert 'question' in q_dict
            assert 'fieldId' in q_dict
            assert 'displayName' in q_dict
            assert q_dict['fieldId'] == test_questions_json[i]['fieldId']
            print(f"    ✓ Question {i+1} structure is correct")
        
        # Test JSON string serialization
        json_str = json.dumps(questions_dict)
        print("  ✓ JSON string serialization successful")
        
        # Test round-trip
        parsed_json = json.loads(json_str)
        round_trip_questions = [QuestionCreate(**q_data) for q_data in parsed_json]
        assert len(round_trip_questions) == len(questions)
        assert round_trip_questions[0].fieldId == 129281
        print("  ✓ Round-trip serialization successful")
        
        print("✓ Questions JSON serialization test passed")
        return True
        
    except Exception as e:
        print(f"✗ Questions JSON serialization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_api_request_format():
    """Test that the API can handle the exact request format specified"""
    print("\nTesting API request format...")
    
    try:
        from app.models import QuestionCreate
        
        # Test the exact format from the user's request
        api_request_data = [
            {"question": "question 1", "fieldId": 129281, "displayName": "State"}
        ]
        
        # Test that this can be parsed into QuestionCreate objects
        questions = [QuestionCreate(**q_data) for q_data in api_request_data]
        
        print(f"  ✓ API request format parsed successfully")
        print(f"    - Question: {questions[0].question}")
        print(f"    - Field ID: {questions[0].fieldId}")
        print(f"    - Display Name: {questions[0].displayName}")
        
        # Test with multiple questions
        multi_question_data = [
            {"question": "What is your state?", "fieldId": 129281, "displayName": "State"},
            {"question": "What is your city?", "fieldId": 129282, "displayName": "City"},
            {"question": "What is your zip code?", "fieldId": 129283, "displayName": "Zip Code"}
        ]
        
        multi_questions = [QuestionCreate(**q_data) for q_data in multi_question_data]
        print(f"  ✓ Multiple questions parsed successfully - {len(multi_questions)} questions")
        
        # Verify unique field IDs
        field_ids = [q.fieldId for q in multi_questions]
        unique_field_ids = set(field_ids)
        if len(field_ids) == len(unique_field_ids):
            print("  ✓ All field IDs are unique")
        else:
            print("  ✗ Duplicate field IDs found")
            return False
        
        print("✓ API request format test passed")
        return True
        
    except Exception as e:
        print(f"✗ API request format test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("Starting tests for updated questions API structure...\n")
    
    tests = [
        ("ChatbotQuestion Database Model", test_question_database_model),
        ("QuestionCreate Pydantic Model", test_question_create_model),
        ("Service Layer Questions", test_service_layer_questions),
        ("Questions JSON Serialization", test_questions_json_serialization),
        ("API Request Format", test_api_request_format)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "="*70)
    print("TEST SUMMARY")
    print("="*70)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:35} {status}")
        if result:
            passed += 1
    
    print("-"*70)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The updated questions API structure is working correctly.")
        print("\nThe API now accepts requests in this format:")
        print('[{"question":"question 1","fieldId":129281,"displayName":"State"}]')
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
