"""Add missing trigger column and credit usage table

Revision ID: 724d608a9454
Revises: bb9a270fd670
Create Date: 2025-07-08 23:11:56.563042

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '724d608a9454'
down_revision: Union[str, None] = 'bb9a270fd670'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Only add what's truly missing - be conservative

    # 1. Create chatbot_credit_usage table if it doesn't exist
    op.create_table('chatbot_credit_usage',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('chatbot_id', sa.String(), nullable=True),
    sa.Column('conversation_id', sa.String(), nullable=True),
    sa.Column('tenant_id', sa.String(), nullable=True),
    sa.Column('question', sa.String(), nullable=False),
    sa.Column('answer', sa.String(), nullable=False),
    sa.Column('credits_used', sa.Integer(), nullable=True),
    sa.Column('has_knowledgebase', sa.Boolean(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chatbot_id'], ['chatbots.id'], ),
    sa.ForeignKeyConstraint(['conversation_id'], ['chatbot_conversations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_credit_usage_chatbot_id'), 'chatbot_credit_usage', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_credit_usage_conversation_id'), 'chatbot_credit_usage', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_chatbot_credit_usage_tenant_id'), 'chatbot_credit_usage', ['tenant_id'], unique=False)

    # 2. Add trigger column to chatbots table if it doesn't exist
    op.add_column('chatbots', sa.Column('trigger', sa.String(), nullable=True))

    # 3. Drop backup table if it exists
    op.drop_table('conversation_token_usage_backup')


def downgrade() -> None:
    # Reverse the changes

    # 1. Drop chatbot_credit_usage table
    op.drop_index(op.f('ix_chatbot_credit_usage_tenant_id'), table_name='chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_conversation_id'), table_name='chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_chatbot_id'), table_name='chatbot_credit_usage')
    op.drop_table('chatbot_credit_usage')

    # 2. Remove trigger column from chatbots table
    op.drop_column('chatbots', 'trigger')

    # 3. Recreate backup table (optional - could be skipped)
    # op.create_table('conversation_token_usage_backup', ...)
