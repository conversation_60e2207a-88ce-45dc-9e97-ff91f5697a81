"""Initial migration

Revision ID: bb9a270fd670
Revises: 
Create Date: 2025-07-08 23:10:26.639228

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bb9a270fd670'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chatbot_credit_usage',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('chatbot_id', sa.String(), nullable=True),
    sa.Column('conversation_id', sa.String(), nullable=True),
    sa.Column('tenant_id', sa.String(), nullable=True),
    sa.Column('question', sa.String(), nullable=False),
    sa.Column('answer', sa.String(), nullable=False),
    sa.Column('credits_used', sa.Integer(), nullable=True),
    sa.Column('has_knowledgebase', sa.Boolean(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chatbot_id'], ['chatbots.id'], ),
    sa.ForeignKeyConstraint(['conversation_id'], ['chatbot_conversations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_credit_usage_chatbot_id'), 'chatbot_credit_usage', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_credit_usage_conversation_id'), 'chatbot_credit_usage', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_chatbot_credit_usage_tenant_id'), 'chatbot_credit_usage', ['tenant_id'], unique=False)
    op.drop_table('conversation_token_usage_backup')
    op.add_column('chatbots', sa.Column('trigger', sa.String(), nullable=True))
    op.alter_column('conversation_token_usage', 'input',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('conversation_token_usage', 'output',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_index('idx_conversation_token_usage_conversation_id', table_name='conversation_token_usage')
    op.drop_index('idx_conversation_token_usage_input_gin', table_name='conversation_token_usage', postgresql_using='gin')
    op.drop_index('idx_conversation_token_usage_output_gin', table_name='conversation_token_usage', postgresql_using='gin')
    op.drop_index('idx_conversation_token_usage_tenant_id', table_name='conversation_token_usage')
    op.create_index(op.f('ix_conversation_token_usage_conversation_id'), 'conversation_token_usage', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_conversation_token_usage_tenant_id'), 'conversation_token_usage', ['tenant_id'], unique=False)
    op.drop_constraint('fk_conversation', 'conversation_token_usage', type_='foreignkey')
    op.alter_column('documents', 'es_index',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('documents', 'es_document_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_index('ix_documents_tenant_id', table_name='documents')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_documents_tenant_id', 'documents', ['tenant_id'], unique=False)
    op.alter_column('documents', 'es_document_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('documents', 'es_index',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_foreign_key('fk_conversation', 'conversation_token_usage', 'chatbot_conversations', ['conversation_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_conversation_token_usage_tenant_id'), table_name='conversation_token_usage')
    op.drop_index(op.f('ix_conversation_token_usage_conversation_id'), table_name='conversation_token_usage')
    op.create_index('idx_conversation_token_usage_tenant_id', 'conversation_token_usage', ['tenant_id'], unique=False)
    op.create_index('idx_conversation_token_usage_output_gin', 'conversation_token_usage', ['output'], unique=False, postgresql_using='gin')
    op.create_index('idx_conversation_token_usage_input_gin', 'conversation_token_usage', ['input'], unique=False, postgresql_using='gin')
    op.create_index('idx_conversation_token_usage_conversation_id', 'conversation_token_usage', ['conversation_id'], unique=False)
    op.alter_column('conversation_token_usage', 'output',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('conversation_token_usage', 'input',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.drop_column('chatbots', 'trigger')
    op.create_table('conversation_token_usage_backup',
    sa.Column('id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('conversation_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tenant_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('message_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('message_content', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('input_tokens', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('output_tokens', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('model', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('timestamp', postgresql.TIMESTAMP(), autoincrement=False, nullable=True)
    )
    op.drop_index(op.f('ix_chatbot_credit_usage_tenant_id'), table_name='chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_conversation_id'), table_name='chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_chatbot_id'), table_name='chatbot_credit_usage')
    op.drop_table('chatbot_credit_usage')
    # ### end Alembic commands ###
